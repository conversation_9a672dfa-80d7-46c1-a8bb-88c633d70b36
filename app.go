package main

import (
	"context"
	"crypto/aes"
	"crypto/cipher"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	goruntime "runtime"
	"time"

	"github.com/wailsapp/wails/v2/pkg/runtime"
)

// App struct
type App struct {
	ctx            context.Context
	cachedPassword string // 缓存的密码，程序运行期间有效
}

// NewApp creates a new App application struct
func NewApp() *App {
	return &App{}
}

// startup is called at application startup
func (a *App) startup(ctx context.Context) {
	// Perform your setup here
	a.ctx = ctx
}

// domReady is called after front-end resources have been loaded
func (a App) domReady(ctx context.Context) {
	// 设置窗口大小和位置：居中显示，可拖动
	go func() {
		// 获取屏幕尺寸
		screens, err := runtime.ScreenGetAll(ctx)
		if err == nil && len(screens) > 0 {
			screen := screens[0] // 使用主屏幕

			// 计算窗口位置和大小
			width := 1024
			height := 1000 // 设置一个合适的高度，不占满屏幕
			x := (screen.Width - width) / 2 // 水平居中
			y := (screen.Height - height) / 2 // 垂直居中

			// 只在窗口不是最大化状态时设置大小和位置
			runtime.WindowSetSize(ctx, width, height)
			runtime.WindowSetPosition(ctx, x, y)
		}
	}()
}

// beforeClose is called when the application is about to quit,
// either by clicking the window close button or calling runtime.Quit.
// Returning true will cause the application to continue, false will continue shutdown as normal.
func (a *App) beforeClose(ctx context.Context) (prevent bool) {
	return false
}

// shutdown is called at application termination
func (a *App) shutdown(ctx context.Context) {
	// Perform your teardown here
}

// AuthConfig 授权配置结构体
type AuthConfig struct {
	ServiceCode      string `json:"serviceCode"`
	CustomerCode     string `json:"customerCode"`
	CustomerProtocol string `json:"customerProtocol"`
	AdminPassword    string `json:"adminPassword"`
	AuthDate         string `json:"authDate"`
	CreatedAt        string `json:"createdAt"`
	UpdatedAt        string `json:"updatedAt"`
}

// EncryptedConfig 加密配置结构体
type EncryptedConfig struct {
	EncryptedData string `json:"encryptedData"`
	CreatedAt     string `json:"createdAt"`
	UpdatedAt     string `json:"updatedAt"`
}

// DecryptedAuthData 解密后的授权数据结构体
type DecryptedAuthData struct {
	Code      string `json:"code"`
	IXieYi    string `json:"iXieYi"`
	IType     string `json:"iType"`
	ISonType  string `json:"iSonType"`
	Time      string `json:"time"`
	Pwd       string `json:"pwd"`
}

// Greet returns a greeting for the given name
func (a *App) Greet(name string) string {
	return fmt.Sprintf("Hello %s, It's show time!", name)
}



// SaveAuthConfig 保存授权配置
func (a *App) SaveAuthConfig(config AuthConfig) (string, error) {
	// 验证密码（如果没有缓存密码，需要先验证）
	if a.cachedPassword == "" {
		return "", fmt.Errorf("请先进行密码验证")
	}

	// 验证输入的密码是否与缓存的密码一致
	if config.AdminPassword != a.cachedPassword {
		return "", fmt.Errorf("密码不正确，请重新验证")
	}

	// 添加时间戳
	now := time.Now().Format("2006-01-02 15:04:05")

	// 生成加密字符串（包含密码）
	encryptedData, err := GetAesString(config.ServiceCode, config.CustomerCode, config.CustomerProtocol, config.AuthDate, config.AdminPassword)
	if err != nil {
		return "", fmt.Errorf("生成加密数据失败: %v", err)
	}

	// 验证授权时间是否过期
	if err := validateAuthDate(encryptedData); err != nil {
		return "", err
	}

	// 创建加密配置对象（用于内部缓存）
	encryptedConfig := EncryptedConfig{
		EncryptedData: encryptedData,
		CreatedAt:     now,
		UpdatedAt:     now,
	}

	// 获取配置文件路径（用于内部缓存）
	configPath, err := getConfigPath()
	if err != nil {
		return "", fmt.Errorf("获取配置路径失败: %v", err)
	}

	// 将加密配置转换为JSON（用于内部缓存）
	configJSON, err := json.MarshalIndent(encryptedConfig, "", "  ")
	if err != nil {
		return "", fmt.Errorf("配置序列化失败: %v", err)
	}

	// 写入内部配置文件
	if err := os.WriteFile(configPath, configJSON, 0644); err != nil {
		return "", fmt.Errorf("写入内部配置文件失败: %v", err)
	}

	// 生成txt文件名：软件授权密钥+日期.txt
	txtFileName := fmt.Sprintf("软件授权密钥%s.txt", config.AuthDate)

	// 获取程序所在目录
	exePath, err := os.Executable()
	if err != nil {
		return "", fmt.Errorf("获取程序路径失败: %v", err)
	}
	exeDir := filepath.Dir(exePath)
	txtFilePath := filepath.Join(exeDir, txtFileName)

	// 直接将encryptedData写入txt文件
	if err := os.WriteFile(txtFilePath, []byte(encryptedData), 0644); err != nil {
		return "", fmt.Errorf("写入授权文件失败: %v", err)
	}

	return fmt.Sprintf("授权文件已成功生成: %s\n授权密钥: %s", txtFilePath, encryptedData), nil
}

// LoadAuthConfig 加载授权配置
func (a *App) LoadAuthConfig() (*EncryptedConfig, error) {
	configPath, err := getConfigPath()
	if err != nil {
		return nil, fmt.Errorf("获取配置路径失败: %v", err)
	}

	// 检查文件是否存在
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		return nil, fmt.Errorf("配置文件不存在")
	}

	// 读取配置文件
	configData, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %v", err)
	}

	// 解析JSON（现在是加密配置）
	var config EncryptedConfig
	if err := json.Unmarshal(configData, &config); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %v", err)
	}

	return &config, nil
}

// getConfigPath 获取配置文件路径
func getConfigPath() (string, error) {
	// 获取程序启动的目录
	exePath, err := os.Executable()
	if err != nil {
		return "", err
	}

	// 获取程序所在目录
	exeDir := filepath.Dir(exePath)

	// 配置文件路径（在程序同目录下）
	configPath := filepath.Join(exeDir, "auth_config.json")

	return configPath, nil
}

// GetConfigPath 获取配置文件路径（供前端调用）
func (a *App) GetConfigPath() (string, error) {
	return getConfigPath()
}

// MinimizeWindow 最小化窗口
func (a *App) MinimizeWindow() {
	runtime.WindowMinimise(a.ctx)
}

// CloseWindow 关闭窗口
func (a *App) CloseWindow() {
	runtime.Quit(a.ctx)
}

// MaximizeWindow 最大化窗口到全屏
func (a *App) MaximizeWindow() {
	// 获取屏幕尺寸
	screens, err := runtime.ScreenGetAll(a.ctx)
	if err == nil && len(screens) > 0 {
		screen := screens[0] // 使用主屏幕

		// 设置窗口大小为屏幕大小
		runtime.WindowSetSize(a.ctx, screen.Width, screen.Height)
		runtime.WindowSetPosition(a.ctx, 0, 0)

		// 也可以使用Wails的最大化功能
		runtime.WindowMaximise(a.ctx)
	}
}

// AESHelper AES加密辅助函数
func AesEncrypt(plaintext, key, iv string) (string, error) {
	// 确保key和iv长度为16字节
	keyBytes := []byte(key)
	ivBytes := []byte(iv)

	if len(keyBytes) != 16 {
		return "", fmt.Errorf("密钥长度必须为16字节")
	}
	if len(ivBytes) != 16 {
		return "", fmt.Errorf("IV长度必须为16字节")
	}

	// 创建AES加密器
	block, err := aes.NewCipher(keyBytes)
	if err != nil {
		return "", err
	}

	// 对明文进行PKCS7填充
	plaintextBytes := pkcs7Padding([]byte(plaintext), aes.BlockSize)

	// 使用CBC模式加密
	mode := cipher.NewCBCEncrypter(block, ivBytes)
	ciphertext := make([]byte, len(plaintextBytes))
	mode.CryptBlocks(ciphertext, plaintextBytes)

	// 返回Base64编码的结果
	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

// pkcs7Padding PKCS7填充
func pkcs7Padding(data []byte, blockSize int) []byte {
	padding := blockSize - len(data)%blockSize
	padtext := make([]byte, padding)
	for i := range padtext {
		padtext[i] = byte(padding)
	}
	return append(data, padtext...)
}

// GetAesString 生成AES加密字符串
func GetAesString(serviceCode, customerCode, customerProtocol, authDate, adminPassword string) (string, error) {
	// 构建JSON字符串，参考C#代码格式，添加pwd字段
	jsonStr := fmt.Sprintf(`{"code":"%s","iXieYi":"%s","iType":"%s","iSonType":"%s","time":"%s","pwd":"%s"}`,
		serviceCode, customerProtocol, serviceCode, customerCode, authDate, adminPassword)

	// 使用固定的密钥和IV
	key := "jAKSMXWzPPfwLrt1"
	iv := "QmgU18iFkPQCLrt2"

	// AES加密
	resultStr, err := AesEncrypt(jsonStr, key, iv)
	if err != nil {
		return "", err
	}

	// 交换第5位和倒数第5位字符
	if len(resultStr) >= 10 { // 确保字符串长度足够
		charArray := []rune(resultStr)
		temp := charArray[4] // 第5位（索引4）
		charArray[4] = charArray[len(charArray)-5] // 倒数第5位
		charArray[len(charArray)-5] = temp
		resultStr = string(charArray)
	}

	return resultStr, nil
}

// AesDecrypt AES解密函数
func AesDecrypt(ciphertext, key, iv string) (string, error) {
	// 确保key和iv长度为16字节
	keyBytes := []byte(key)
	ivBytes := []byte(iv)

	if len(keyBytes) != 16 {
		return "", fmt.Errorf("密钥长度必须为16字节")
	}
	if len(ivBytes) != 16 {
		return "", fmt.Errorf("IV长度必须为16字节")
	}

	// Base64解码
	ciphertextBytes, err := base64.StdEncoding.DecodeString(ciphertext)
	if err != nil {
		return "", err
	}

	// 创建AES解密器
	block, err := aes.NewCipher(keyBytes)
	if err != nil {
		return "", err
	}

	// 使用CBC模式解密
	mode := cipher.NewCBCDecrypter(block, ivBytes)
	plaintext := make([]byte, len(ciphertextBytes))
	mode.CryptBlocks(plaintext, ciphertextBytes)

	// 移除PKCS7填充
	plaintext = pkcs7UnPadding(plaintext)

	return string(plaintext), nil
}

// pkcs7UnPadding 移除PKCS7填充
func pkcs7UnPadding(data []byte) []byte {
	if len(data) == 0 {
		return data
	}

	padding := int(data[len(data)-1])
	if padding > len(data) || padding == 0 {
		return data
	}

	return data[:len(data)-padding]
}

// validateAuthDate 验证授权时间是否过期
func validateAuthDate(encryptedData string) error {
	// 解密数据获取授权时间
	authData, err := decryptAuthData(encryptedData)
	if err != nil {
		return fmt.Errorf("解密授权数据失败: %v", err)
	}

	// 解析授权时间
	authTime, err := time.Parse("2006-01-02", authData.Time)
	if err != nil {
		return fmt.Errorf("授权时间格式错误: %v", err)
	}

	// 获取当前时间（只比较日期部分）
	now := time.Now()
	currentDate := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())

	// 检查是否过期（授权时间应该大于等于当前时间）
	if authTime.Before(currentDate) {
		return fmt.Errorf("授权已过期，授权截止日期: %s，当前日期: %s",
			authTime.Format("2006-01-02"),
			currentDate.Format("2006-01-02"))
	}

	return nil
}

// decryptAuthData 解密授权数据
func decryptAuthData(encryptedData string) (*DecryptedAuthData, error) {
	// 先恢复字符交换（第5位和倒数第5位）
	if len(encryptedData) >= 10 {
		charArray := []rune(encryptedData)
		temp := charArray[4] // 第5位（索引4）
		charArray[4] = charArray[len(charArray)-5] // 倒数第5位
		charArray[len(charArray)-5] = temp
		encryptedData = string(charArray)
	}

	// 使用固定的密钥和IV进行AES解密
	key := "jAKSMXWzPPfwLrt1"
	iv := "QmgU18iFkPQCLrt2"

	// AES解密
	decryptedJSON, err := AesDecrypt(encryptedData, key, iv)
	if err != nil {
		return nil, fmt.Errorf("AES解密失败: %v", err)
	}

	// 解析JSON
	var authData DecryptedAuthData
	if err := json.Unmarshal([]byte(decryptedJSON), &authData); err != nil {
		return nil, fmt.Errorf("解析授权数据JSON失败: %v", err)
	}

	return &authData, nil
}

// ValidatePassword 验证密码并缓存
func (a *App) ValidatePassword(password string) (bool, error) {
	// 如果已经有缓存的密码，直接比较
	if a.cachedPassword != "" && a.cachedPassword == password {
		return true, nil
	}

	// 尝试加载现有配置来验证密码
	config, err := a.LoadAuthConfig()
	if err != nil {
		// 如果没有现有配置，这是第一次使用，直接缓存密码
		a.cachedPassword = password
		return true, nil
	}

	// 解密现有配置中的密码进行验证
	authData, err := decryptAuthData(config.EncryptedData)
	if err != nil {
		return false, fmt.Errorf("解密现有配置失败: %v", err)
	}

	// 验证密码是否匹配
	if authData.Pwd == password {
		a.cachedPassword = password
		return true, nil
	}

	return false, fmt.Errorf("密码不正确")
}

// ClearPasswordCache 清除密码缓存
func (a *App) ClearPasswordCache() {
	a.cachedPassword = ""
}

// IsPasswordCached 检查是否已缓存密码
func (a *App) IsPasswordCached() bool {
	return a.cachedPassword != ""
}

// LoadAndDecryptConfig 加载并解密配置文件，返回解密后的数据
func (a *App) LoadAndDecryptConfig() (*DecryptedAuthData, error) {
	// 加载加密配置
	config, err := a.LoadAuthConfig()
	if err != nil {
		return nil, fmt.Errorf("加载配置文件失败: %v", err)
	}

	// 解密配置数据
	authData, err := decryptAuthData(config.EncryptedData)
	if err != nil {
		return nil, fmt.Errorf("解密配置数据失败: %v", err)
	}

	return authData, nil
}

// GetTxtFilePath 获取生成的txt文件路径
func (a *App) GetTxtFilePath(authDate string) (string, error) {
	// 生成txt文件名：软件授权密钥+日期.txt
	txtFileName := fmt.Sprintf("软件授权密钥%s.txt", authDate)

	// 获取程序所在目录
	exePath, err := os.Executable()
	if err != nil {
		return "", fmt.Errorf("获取程序路径失败: %v", err)
	}
	exeDir := filepath.Dir(exePath)
	txtFilePath := filepath.Join(exeDir, txtFileName)

	return txtFilePath, nil
}

// OpenFileFolder 打开文件所在的文件夹
func (a *App) OpenFileFolder() error {
	// 获取程序所在目录
	exePath, err := os.Executable()
	if err != nil {
		return fmt.Errorf("获取程序路径失败: %v", err)
	}
	exeDir := filepath.Dir(exePath)

	// 根据操作系统打开文件夹
	var cmdName string
	var args []string

	switch goruntime.GOOS {
	case "windows":
		cmdName = "explorer"
		args = []string{exeDir}
	case "darwin":
		cmdName = "open"
		args = []string{exeDir}
	case "linux":
		cmdName = "xdg-open"
		args = []string{exeDir}
	default:
		return fmt.Errorf("不支持的操作系统: %s", goruntime.GOOS)
	}

	// 执行命令
	cmd := exec.Command(cmdName, args...)
	return cmd.Start()
}
