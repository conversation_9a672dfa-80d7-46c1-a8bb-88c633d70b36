// 全局变量
let isPasswordValidated = false;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeForm();
    checkPasswordCache();
    loadExistingConfig();

    // 监听标题栏更新事件
    if (window.wails && window.wails.Events) {
        window.wails.Events.On('titlebar-updated', function(data) {
            console.log('标题栏更新成功:', data);
        });

        window.wails.Events.On('titlebar-error', function(error) {
            console.log('标题栏更新失败:', error);
        });
    }
});

// 初始化表单
function initializeForm() {
    const form = document.getElementById('authForm');
    const submitBtn = document.getElementById('submitBtn');
    const resultDiv = document.getElementById('result');

    // 表单提交事件
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        handleFormSubmit();
    });

    // 添加输入框焦点效果
    const inputs = document.querySelectorAll('.form-input');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });

        input.addEventListener('blur', function() {
            if (!this.value) {
                this.parentElement.classList.remove('focused');
            }
        });

        // 如果输入框有值，保持焦点样式
        if (input.value) {
            input.parentElement.classList.add('focused');
        }
    });
}

// 处理表单提交
function handleFormSubmit() {
    const formData = getFormData();

    // 验证表单
    if (!validateForm(formData)) {
        return;
    }

    // 检查是否需要密码验证
    if (!isPasswordValidated) {
        showPasswordModal();
        return;
    }

    // 显示加载状态
    showLoading(true);

    // 调用后端保存函数
    saveAuthConfig(formData);
}

// 获取表单数据
function getFormData() {
    // 获取软件识别码字段
    const softCode = document.getElementById('softCode').value.trim();

    // 获取客服代码字段
    const customerServiceCode = document.getElementById('serviceCode').value.trim();

    return {
        serviceCode: softCode, // 软件识别码
        customerCode: document.getElementById('customerCode').value.trim(),
        customerProtocol: customerServiceCode, // 客服代码
        adminPassword: 'default123', // 设置默认密码，或者可以添加密码输入字段
        authDate: document.getElementById('authDate').value
    };
}

// 验证表单
function validateForm(data) {
    const requiredFields = [
        { field: 'serviceCode', name: '软件识别码', elementId: 'softCode' },
        { field: 'authDate', name: '授权日期', elementId: 'authDate' }
    ];

    for (let item of requiredFields) {
        if (!data[item.field]) {
            showMessage(`请输入${item.name}`, 'error');
            // 聚焦到对应字段
            document.getElementById(item.elementId).focus();
            return false;
        }
    }

    // 验证日期
    const selectedDate = new Date(data.authDate);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (selectedDate < today) {
        showMessage('授权日期不能早于今天', 'error');
        document.getElementById('authDate').focus();
        return false;
    }

    // 检查客服代码和客户代码是否已自动填充
    if (!data.customerProtocol || !data.customerCode) {
        showMessage('请先加载现有配置文件以获取客服代码和客户代码', 'error');
        return false;
    }

    return true;
}

// 保存授权配置
function saveAuthConfig(data) {
    console.log('[DEBUG] saveAuthConfig called with data:', data);

    // 如果存在Go后端函数，调用它
    if (window.go && window.go.main && window.go.main.App && window.go.main.App.SaveAuthConfig) {
        console.log('[DEBUG] Calling backend SaveAuthConfig...');
        window.go.main.App.SaveAuthConfig(data)
            .then(result => {
                showLoading(false);
                // 显示详细的成功信息，包含文件路径
                showMessage('授权文件生成成功！', 'success');
                console.log('保存结果:', result);

                // 显示文件路径信息
                setTimeout(() => {
                    showMessage(result, 'success');
                }, 1000);
            })
            .catch(err => {
                showLoading(false);
                // 检查是否是密钥相关错误
                const errorMessage = err.toString();
                if (errorMessage.includes('密钥不存在') || errorMessage.includes('请联系管理员')) {
                    showMessage(errorMessage, 'error');
                } else if (errorMessage.includes('授权已过期')) {
                    showMessage(errorMessage, 'error');
                } else if (errorMessage.includes('密码不正确')) {
                    showMessage(errorMessage, 'error');
                } else {
                    showMessage('生成失败：' + err, 'error');
                }
                console.error('保存错误:', err);
            });
    } else {
        setTimeout(() => {
            showLoading(false);
            showMessage('无法连接到后端服务！', 'error');
            console.log('保存的数据:', data);
        }, 1500);
    }
}

// 显示消息
function showMessage(message, type = 'success') {
    const resultDiv = document.getElementById('result');
    resultDiv.textContent = message;
    resultDiv.className = `result-message ${type} show`;

    // 3秒后自动隐藏消息
    setTimeout(() => {
        resultDiv.classList.remove('show');
    }, 3000);
}

// 显示/隐藏加载状态
function showLoading(isLoading) {
    const submitBtn = document.getElementById('submitBtn');
    const btnText = submitBtn.querySelector('.btn-text');

    if (isLoading) {
        submitBtn.disabled = true;
        btnText.textContent = '保存中...';
        submitBtn.style.opacity = '0.7';
    } else {
        submitBtn.disabled = false;
        btnText.textContent = '确定保存';
        submitBtn.style.opacity = '1';
    }
}

// 窗口控制函数
function minimizeWindow() {
    if (window.go && window.go.main && window.go.main.App && window.go.main.App.MinimizeWindow) {
        window.go.main.App.MinimizeWindow();
    }
}

function closeWindow() {
    if (window.go && window.go.main && window.go.main.App && window.go.main.App.CloseWindow) {
        window.go.main.App.CloseWindow();
    }
}

// 兼容旧版本的greet函数（如果需要）
function greet() {
    const name = document.getElementById('name')?.value || 'World';
    showMessage(`Hello, ${name}!`, 'success');
}

// 检查密码缓存状态
function checkPasswordCache() {
    if (window.go && window.go.main && window.go.main.App && window.go.main.App.IsPasswordCached) {
        window.go.main.App.IsPasswordCached()
            .then(result => {
                isPasswordValidated = result;
                console.log('密码缓存状态:', result);
            })
            .catch(err => {
                console.error('检查密码缓存失败:', err);
                isPasswordValidated = false;
            });
    }
}

// 加载现有配置并填充表单
function loadExistingConfig() {
    console.log('开始加载现有配置...');

    // 检查页面元素
    const softCodeField = document.getElementById('softCode');
    const serviceCodeField = document.getElementById('serviceCode');
    const customerCodeField = document.getElementById('customerCode');
    console.log('页面元素检查:');
    console.log('- softCode字段存在:', !!softCodeField);
    console.log('- serviceCode字段存在:', !!serviceCodeField);
    console.log('- customerCode字段存在:', !!customerCodeField);

    if (window.go && window.go.main && window.go.main.App && window.go.main.App.LoadAndDecryptConfig) {
        window.go.main.App.LoadAndDecryptConfig()
            .then(configData => {
                console.log('加载的配置数据:', configData);
                console.log('配置数据详情:');
                console.log('- Code:', configData.Code);
                console.log('- IXieYi:', configData.IXieYi);
                console.log('- IType:', configData.IType);
                console.log('- ISonType:', configData.ISonType);
                console.log('- Time:', configData.Time);
                console.log('- Pwd:', configData.Pwd);

                // 填充表单字段
                const customerServiceCode = configData.IType;
                if (customerServiceCode) {
                    // 填充客服代码字段（第二个serviceCode，即disabled的那个）
                    const serviceCodeFields = document.getElementById('serviceCode');
                    if (serviceCodeFields) {
                        serviceCodeFields.value = customerServiceCode; // 第二个字段是客服代码
                        console.log('已填充客服代码:', customerServiceCode);
                    } else {
                        console.log('未找到客服代码字段，当前字段数量:', serviceCodeFields.length);
                    }
                } else {
                    console.log('配置数据中没有客服代码字段 (IType)');
                }

                if (configData.ISonType) {
                    // 填充客户代码字段
                    const customerCodeField = document.getElementById('customerCode');
                    if (customerCodeField) {
                        customerCodeField.value = configData.ISonType;
                        console.log('已填充客户代码:', configData.ISonType);
                    } else {
                        console.log('未找到客户代码字段');
                    }
                } else {
                    console.log('配置数据中没有ISonType字段');
                }

                // 可以根据需要填充其他字段
                console.log('表单字段填充完成');
            })
            .catch(err => {
                console.log('没有找到现有配置文件或解密失败:', err);
                console.log('错误详情:', err.toString());
                // 这是正常情况，可能是第一次使用
            });
    } else {
        console.log('后端服务不可用，无法加载配置');
    }
}

// 显示密码验证弹窗
function showPasswordModal() {
    const modal = document.getElementById('passwordModal');
    const passwordInput = document.getElementById('modalPassword');
    const errorDiv = document.getElementById('modalError');

    // 清空输入和错误信息
    passwordInput.value = '';
    errorDiv.textContent = '';

    // 显示弹窗
    modal.style.display = 'block';

    // 聚焦到密码输入框
    setTimeout(() => {
        passwordInput.focus();
    }, 100);

    // 监听回车键
    passwordInput.addEventListener('keydown', function(e) {
        if (e.key === 'Enter') {
            validatePassword();
        }
    });
}

// 关闭密码验证弹窗
function closePasswordModal() {
    const modal = document.getElementById('passwordModal');
    modal.style.display = 'none';
}

// 验证密码
function validatePassword() {
    const passwordInput = document.getElementById('modalPassword');
    const errorDiv = document.getElementById('modalError');
    const confirmBtn = document.querySelector('.confirm-btn');

    const password = passwordInput.value.trim();

    if (!password) {
        errorDiv.textContent = '请输入密码';
        passwordInput.focus();
        return;
    }

    // 禁用确认按钮，显示加载状态
    confirmBtn.disabled = true;
    confirmBtn.textContent = '验证中...';
    errorDiv.textContent = '';

    // 调用后端验证密码
    if (window.go && window.go.main && window.go.main.App && window.go.main.App.ValidatePassword) {
        window.go.main.App.ValidatePassword(password)
            .then(result => {
                if (result) {
                    // 密码验证成功
                    isPasswordValidated = true;
                    closePasswordModal();
                    showMessage('密码验证成功', 'success');

                    // 自动提交表单
                    setTimeout(() => {
                        handleFormSubmit();
                    }, 500);
                } else {
                    // 密码验证失败
                    errorDiv.textContent = '密码不正确';
                    passwordInput.focus();
                    passwordInput.select();
                }
            })
            .catch(err => {
                console.error('密码验证错误:', err);
                errorDiv.textContent = err.toString().replace('Error: ', '');
                passwordInput.focus();
                passwordInput.select();
            })
            .finally(() => {
                // 恢复确认按钮状态
                confirmBtn.disabled = false;
                confirmBtn.textContent = '确认';
            });
    } else {
        errorDiv.textContent = '无法连接到后端服务';
        confirmBtn.disabled = false;
        confirmBtn.textContent = '确认';
    }
}

// 点击弹窗外部关闭弹窗
window.onclick = function(event) {
    const modal = document.getElementById('passwordModal');
    if (event.target === modal) {
        closePasswordModal();
    }
}

// 打开文件夹
function openFileFolder() {
    if (window.go && window.go.main && window.go.main.App && window.go.main.App.OpenFileFolder) {
        window.go.main.App.OpenFileFolder()
            .then(() => {
                showMessage('文件夹已打开', 'success');
            })
            .catch(err => {
                console.error('打开文件夹失败:', err);
                showMessage('打开文件夹失败：' + err, 'error');
            });
    } else {
        showMessage('无法连接到后端服务', 'error');
    }
}