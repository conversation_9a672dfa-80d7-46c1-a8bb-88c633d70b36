/* 重置和基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: "Nunito", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    line-height: 1.6;
    overflow: hidden;
}

@font-face {
    font-family: "Nunito";
    font-style: normal;
    font-weight: 400;
    src: local(""), url("assets/fonts/nunito-v16-latin-regular.woff2") format("woff2");
}

/* 自定义标题栏 - 隐藏，使用原生标题栏 */
.custom-titlebar {
    display: none;
}

.titlebar-content {
    display: flex;
    align-items: center;
    gap: 8px;
    color: white;
    font-size: 14px;
    font-weight: 500;
    flex: 1;
    cursor: move;
}

.titlebar-icon {
    font-size: 16px;
}

.titlebar-title {
    font-family: inherit;
}

.titlebar-controls {
    display: flex;
    gap: 0;
}

.titlebar-btn {
    width: 46px;
    height: 32px;
    border: none;
    background: transparent;
    color: white;
    font-size: 16px;
    font-weight: 300;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s ease;
}

.titlebar-btn:hover {
    background: rgba(255, 255, 255, 0.1);
}

.close-btn:hover {
    background: #e81123;
}

.minimize-btn {
    font-size: 14px;
}

/* 容器布局 */
.container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    position: relative;
}

.container::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
    z-index: -1;
}

/* 头部样式 */
.header {
    text-align: center;
    padding: .1rem .1rem;
    position: relative;
}

.logo-container {
    max-width: 600px;
    margin: 0 auto;
}

.logo {
    width: 60px;
    height: 60px;
    margin: 0 auto 0.8rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
}

.logo::before {
    content: '🔐';
    font-size: 1.5rem;
    color: white;
}

.title {
    font-size: 2rem;
    font-weight: 700;
    color: white;
    margin-bottom: 0.3rem;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    letter-spacing: -0.02em;
}

.subtitle {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 300;
    letter-spacing: 0.05em;
    text-transform: uppercase;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 1rem 1rem;
}

.form-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 2rem;
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.1),
        0 8px 25px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.2);
    max-width: 800px;
    width: 90%;
    position: relative;
    overflow: hidden;
    margin: auto;
}

.form-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.form-header {
    text-align: center;
    margin-bottom: 1.5rem;
}

.form-header h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.8rem;
}

.form-divider {
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    margin: 0 auto;
    border-radius: 2px;
}

/* 表单样式 */
.auth-form {
    position: relative;
}

.form-grid {
    display: grid;
    gap: 1.5rem;
}

.form-group {
    position: relative;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #4a5568;
    font-size: 0.9rem;
}

.label-text {
    margin-right: 0.25rem;
}

.required {
    color: #e53e3e;
    font-weight: 600;
}

.form-input {
    width: 100%;
    padding: 0.8rem 0 0.8rem 0;
    border: none;
    border-bottom: 2px solid #e2e8f0;
    background: transparent;
    font-size: 1rem;
    color: #2d3748;
    transition: all 0.3s ease;
    outline: none;
    font-family: inherit;
}

.form-input::placeholder {
    color: #a0aec0;
    transition: all 0.3s ease;
}

.form-input:focus {
    border-bottom-color: #667eea;
}

.form-input:focus::placeholder {
    opacity: 0;
    transform: translateY(-10px);
}

.input-underline {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    transition: width 0.3s ease;
}

.form-input:focus + .input-underline {
    width: 100%;
}

/* 日期输入特殊样式 */
.form-input[type="date"] {
    color: #2d3748;
    cursor: pointer;
}

.form-input[type="date"]::-webkit-calendar-picker-indicator {
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    border-radius: 3px;
    cursor: pointer;
    padding: 2px;
}

/* 密码输入样式 */
.form-input[type="password"] {
    letter-spacing: 0.1em;
}

/* 表单操作区域 */
.form-actions {
    margin-top: 2rem;
    text-align: center;
}

.submit-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 0.8rem 2.5rem;
    border-radius: 50px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
    min-width: 180px;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
}

.submit-btn:active {
    transform: translateY(0);
}

.btn-text {
    position: relative;
    z-index: 2;
}

.btn-ripple {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.submit-btn:active .btn-ripple {
    width: 300px;
    height: 300px;
}

/* 结果消息样式 */
.result-message {
    margin-top: 2rem;
    padding: 1rem;
    border-radius: 10px;
    text-align: center;
    font-weight: 500;
    opacity: 0;
    transform: translateY(10px);
    transition: all 0.3s ease;
}

.result-message.show {
    opacity: 1;
    transform: translateY(0);
}

.result-message.success {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(72, 187, 120, 0.3);
}

.result-message.error {
    background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(245, 101, 101, 0.3);
}

/* 页脚样式 */
.footer {
    text-align: center;
    padding: .5rem;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.85rem;
}

/* 响应式设计 */
/* 大屏幕优化 - 最大化时的样式 */
@media (min-width: 1400px) {
    .form-container {
        max-width: 1000px;
        padding: 3rem;
    }

    .form-grid {
        gap: 2.5rem;
    }

    .form-input {
        padding: 1rem 0 1rem 0;
        font-size: 1.1rem;
    }

    .submit-btn {
        padding: 1rem 3rem;
        font-size: 1.1rem;
        min-width: 220px;
    }
}

@media (max-width: 768px) {
    .title {
        font-size: 2rem;
    }

    .form-container {
        padding: .5rem .5rem;
        margin: .5rem;
        border-radius: 15px;
    }

    .form-grid {
        gap: 1.5rem;
    }

    .submit-btn {
        padding: 0.875rem 2rem;
        font-size: 1rem;
        min-width: 160px;
    }
}

@media (max-width: 480px) {
    .header {
        padding: .1rem .1rem;
    }

    .title {
        font-size: 1.75rem;
    }

    .subtitle {
        font-size: 0.875rem;
    }

    .form-container {
        padding: .1rem .1rem;
    }

    .form-header h2 {
        font-size: 1.5rem;
    }

    .modal-content {
        margin: 20% auto;
        width: 95%;
    }
}

/* 密码验证弹窗样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    margin: 15% auto;
    padding: 0;
    border: none;
    border-radius: 15px;
    width: 90%;
    max-width: 400px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    padding: 20px 25px 15px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: #333;
    font-size: 18px;
    font-weight: 600;
}

.close {
    color: #aaa;
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close:hover,
.close:focus {
    color: #667eea;
}

.modal-body {
    padding: 20px 25px;
}

.modal-body p {
    margin: 0 0 20px 0;
    color: #666;
    font-size: 14px;
}

.modal-error {
    color: #e74c3c;
    font-size: 12px;
    margin-top: 10px;
    min-height: 16px;
}

.modal-footer {
    padding: 15px 25px 20px;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.modal-btn {
    padding: 8px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.cancel-btn {
    background: #f8f9fa;
    color: #666;
    border: 1px solid #dee2e6;
}

.cancel-btn:hover {
    background: #e9ecef;
    color: #495057;
}

.confirm-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.confirm-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.confirm-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}
