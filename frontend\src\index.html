<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=Edge, Chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" type="image/png" href="data:image/png;base64,iVBORw0KGgo=">
    <title>B30授权工具</title>
    <!-- Main Source Files -->
    <link rel="stylesheet" href="main.css" />
    <script src="main.js"></script>
</head>

<body id="app" class="app">
    <div class="container">
        <!-- 自定义标题栏 -->
        <div class="custom-titlebar">
            <div class="titlebar-content" data-wails-drag>
                <div class="titlebar-icon">🔐</div>
                <div class="titlebar-title">B30授权工具</div>
            </div>
            <div class="titlebar-controls" data-wails-no-drag>
                <button class="titlebar-btn minimize-btn" onclick="minimizeWindow()" data-wails-no-drag>−</button>
                <button class="titlebar-btn close-btn" onclick="closeWindow()" data-wails-no-drag>×</button>
            </div>
        </div>

        <header class="header">
            <div class="logo-container">
                <div class="logo"></div>
                <h1 class="title">B30授权工具</h1>
                <p class="subtitle">B30 Authorization Tool</p>
            </div>
        </header>
        <main class="main-content">
            <div class="form-container">
                <form class="auth-form" id="authForm">
                    <div class="form-grid">
                             <div class="form-group">
                            <label for="softCode" class="form-label">
                                <span class="label-text">软件识别码</span>
                                <span class="required">*</span>
                            </label>
                            <input type="text" id="softCode" name="softCode" class="form-input"
                                   placeholder="请输入软件识别码" required>
                            <div class="input-underline"></div>
                        </div>
                        <div class="form-group">
                            <label for="authDate" class="form-label">
                                <span class="label-text">软件授权日期</span>
                                <span class="required">*</span>
                            </label>
                            <input type="date" id="authDate" name="authDate" class="form-input" required>
                            <div class="input-underline"></div>
                        </div>
                    </div>
                    <div class="form-group">
                            <label for="serviceCode" class="form-label">
                                <span class="label-text">客服代码</span>
                                <span class="required">*</span>
                            </label>
                            <input type="text" id="serviceCode" name="serviceCode" class="form-input"
                                   placeholder="请输入客服代码" disabled>
                            <div class="input-underline"></div>
                        </div>

                        <div class="form-group">
                            <label for="customerCode" class="form-label">
                                <span class="label-text">客户代码</span>
                                <span class="required">*</span>
                            </label>
                            <input type="text" id="customerCode" name="customerCode" class="form-input"
                                   placeholder="请输入客户代码" disabled>
                            <div class="input-underline"></div>
                        </div>


                    <div class="form-actions">
                        <button type="submit" class="submit-btn" id="submitBtn">
                            <span class="btn-text">生成授权文件</span>
                            <div class="btn-ripple"></div>
                        </button>
                        <button type="button" class="open-folder-btn" id="openFolderBtn" onclick="openFileFolder()">
                            <span class="btn-text">打开文件夹</span>
                        </button>
                    </div>
                </form>

                <div class="result-message" id="result"></div>
            </div>
        </main>

        <footer class="footer">
            <p>&copy; 2025 B30授权工具. All rights reserved. </p>
        </footer>
    </div>

    <!-- 密码验证弹窗 -->
    <div id="passwordModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>密码验证</h3>
                <span class="close" onclick="closePasswordModal()">&times;</span>
            </div>
            <div class="modal-body">
                <p>请输入管理密码以继续操作：</p>
                <div class="form-group">
                    <input type="password" id="modalPassword" class="form-input"
                           placeholder="请输入管理密码" autocomplete="off">
                    <div class="input-underline"></div>
                </div>
                <div id="modalError" class="modal-error"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="modal-btn cancel-btn" onclick="closePasswordModal()">取消</button>
                <button type="button" class="modal-btn confirm-btn" onclick="validatePassword()">确认</button>
            </div>
        </div>
    </div>
</body>

</html>
